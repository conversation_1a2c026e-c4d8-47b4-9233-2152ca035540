<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>Simple Test Form</h3>
                    </div>
                    <div class="card-body">
                        <!-- Messages -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }}">{{ message }}</div>
                            {% endfor %}
                        {% endif %}
                        
                        <div class="alert alert-info">
                            <strong>Test Info:</strong><br>
                            Request method: {{ request.method }}<br>
                            Path: {{ request.path }}<br>
                            User: {{ request.user }}<br>
                            Authenticated: {{ request.user.is_authenticated }}
                        </div>
                        
                        <form method="post">
                            {% csrf_token %}
                            
                            <div class="mb-3">
                                <label class="form-label">Test Field</label>
                                <input type="text" class="form-control" name="test_field" value="test123" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Submit Test</button>
                        </form>
                        
                        {% if request.method == 'POST' %}
                        <div class="alert alert-success mt-3">
                            <strong>POST Data Received:</strong><br>
                            {% for key, value in request.POST.items %}
                                {{ key }}: {{ value }}<br>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('Form submitted!');
            console.log('Form data:', new FormData(this));
        });
    </script>
</body>
</html>
