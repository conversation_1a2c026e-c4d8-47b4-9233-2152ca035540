<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Signup - AI Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .signup-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 2rem 0;
        }
        .signup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .signup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .signup-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="signup-card">
                        <div class="signup-header">
                            <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                            <h2 class="mb-0">Student Registration</h2>
                            <p class="mb-0 opacity-75">Join the AI Attendance System</p>
                        </div>
                        
                        <div class="signup-body">
                            <!-- Messages -->
                            {% if messages %}
                                {% for message in messages %}
                                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                        <i class="fas fa-info-circle me-2"></i>{{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                            
                            <!-- WORKING FORM - NO COMPLEX JAVASCRIPT -->
                            <form method="post" id="signupForm">
                                {% csrf_token %}
                                
                                <div class="mb-3">
                                    <label for="student_id" class="form-label">
                                        <i class="fas fa-id-card me-2"></i>Student ID *
                                    </label>
                                    <input type="text" class="form-control" id="student_id" name="student_id" 
                                           placeholder="Enter your student ID" required>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="first_name" class="form-label">
                                                <i class="fas fa-user me-2"></i>First Name *
                                            </label>
                                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                                   placeholder="First name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="last_name" class="form-label">
                                                <i class="fas fa-user me-2"></i>Last Name *
                                            </label>
                                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                                   placeholder="Last name" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Email Address *
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="<EMAIL>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="phone" class="form-label">
                                        <i class="fas fa-phone me-2"></i>Phone Number
                                    </label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           placeholder="Optional phone number">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Password *
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="Create a secure password" required>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="confirm_password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Confirm Password *
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           placeholder="Confirm your password" required>
                                </div>
                                
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-user-plus me-2"></i>Create My Account
                                    </button>
                                </div>
                            </form>
                            
                            <div class="text-center">
                                <p class="mb-2">Already have an account?</p>
                                <a href="{% url 'student_login' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In Instead
                                </a>
                            </div>
                            
                            <!-- Debug Info -->
                            <div class="mt-4 p-3 bg-light rounded">
                                <small class="text-muted">
                                    <strong>System Status:</strong><br>
                                    <i class="fas fa-check-circle text-success me-1"></i>Form Ready<br>
                                    <i class="fas fa-server text-primary me-1"></i>Server: {{ request.get_host }}<br>
                                    <i class="fas fa-clock text-info me-1"></i>Time: {% now "Y-m-d H:i:s" %}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- MINIMAL JavaScript - Only for UI feedback -->
    <script>
        console.log('🚀 Signup page loaded successfully');
        
        document.getElementById('signupForm').addEventListener('submit', function(e) {
            console.log('📝 Form submission started');
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...';
            submitBtn.disabled = true;
            
            // Re-enable after 10 seconds in case of issues
            setTimeout(function() {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 10000);
            
            console.log('✅ Form will submit normally');
            // Let form submit naturally - no preventDefault
        });
        
        console.log('✅ JavaScript initialized successfully');
    </script>
</body>
</html>
