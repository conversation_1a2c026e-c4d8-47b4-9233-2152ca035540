<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Session - {{ class.code }} - AI Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .attendance-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .attendance-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .camera-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        #video {
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            background-color: #000;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: transform 0.2s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: transform 0.2s ease;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: transform 0.2s ease;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, #c82333 0%, #d91a72 100%);
        }
        
        .student-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 10px;
            transition: transform 0.2s ease;
        }
        
        .student-card:hover {
            transform: translateY(-2px);
        }
        
        .status-present {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .status-pending {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }
        
        /* Captured image styles */
        #capturedImageDiv {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        #capturedImage {
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        #capturedImage:hover {
            transform: scale(1.02);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="attendance-card">
                    <div class="attendance-header">
                        <i class="fas fa-camera fa-3x mb-3"></i>
                        <h2 class="mb-2">Attendance Session</h2>
                        <h4 class="mb-0 opacity-75">{{ class.code }} - {{ class.name }}</h4>
                        <p class="mb-0 opacity-75">{{ today|date:"F d, Y" }}</p>
                        {% if session_created %}
                            <small class="badge bg-success mt-2">New Session Started</small>
                        {% else %}
                            <small class="badge bg-info mt-2">Session Resumed</small>
                        {% endif %}
                    </div>
                    
                    <div class="row p-4">
                        <!-- Camera Section -->
                        <div class="col-md-8">
                            <div class="camera-section">
                                <h5 class="mb-3">
                                    <i class="fas fa-camera me-2"></i>Face Recognition Camera
                                </h5>
                                
                                <!-- Messages -->
                                {% if messages %}
                                    {% for message in messages %}
                                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                            {{ message }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                                
                                <!-- Camera Display -->
                                <div class="text-center mb-4">
                                    <video id="video" width="100%" height="400" autoplay class="d-none"></video>
                                    <div id="cameraPlaceholder" class="d-flex align-items-center justify-content-center" 
                                         style="width: 100%; height: 400px; background: #f8f9fa; border-radius: 15px; border: 2px dashed #dee2e6;">
                                        <div class="text-center">
                                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">Click "Start Camera" to begin attendance</p>
                                        </div>
                                    </div>
                                    <canvas id="canvas" style="display: none;"></canvas>
                                </div>
                                
                                <!-- Camera Controls -->
                                <div class="text-center mb-4">
                                    <button id="startCamera" class="btn btn-primary btn-lg me-2">
                                        <i class="fas fa-video me-2"></i>Start Camera
                                    </button>
                                    <button id="markAttendance" class="btn btn-success btn-lg me-2" disabled>
                                        <i class="fas fa-user-check me-2"></i>Mark Attendance
                                    </button>
                                    <button id="stopCamera" class="btn btn-danger btn-lg" disabled>
                                        <i class="fas fa-stop me-2"></i>Stop Camera
                                    </button>
                                </div>
                                
                                <!-- Recognition Status -->
                                <div id="recognitionStatus" class="alert" style="display: none;"></div>
                                
                                <!-- Instructions -->
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>Instructions for Students:
                                    </h6>
                                    <ol class="mb-0">
                                        <li>Wait for the lecturer to start the camera</li>
                                        <li>Position your face in the center of the camera</li>
                                        <li>Ensure good lighting on your face</li>
                                        <li>Click "Mark Attendance" when ready</li>
                                        <li>Wait for confirmation message</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Student List Section -->
                        <div class="col-md-4">
                            <h5 class="mb-3">
                                <i class="fas fa-users me-2"></i>Enrolled Students
                                <span class="badge bg-primary ms-2">{{ today_attendance.count }}/{{ enrolled_students.count }}</span>
                            </h5>

                            {% if enrolled_students.count == 0 %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No students enrolled in this class yet.
                                </div>
                            {% else %}
                                <div style="max-height: 600px; overflow-y: auto;">
                                    {% for status in attendance_status %}
                                        <div class="card student-card {% if status.is_marked %}status-present{% else %}status-pending{% endif %}">
                                            <div class="card-body py-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="mb-0">{{ status.student.full_name }}</h6>
                                                        <small class="opacity-75">{{ status.student.student_id }}</small>
                                                    </div>
                                                    <div class="text-end">
                                                        {% if status.is_marked %}
                                                            <i class="fas fa-check-circle fa-lg"></i>
                                                            <br><small>{{ status.attendance.timestamp|time:"H:i" }}</small>
                                                        {% else %}
                                                            <i class="fas fa-clock fa-lg"></i>
                                                            <br><small>Pending</small>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            
                            <!-- Back to Dashboard -->
                            <div class="text-center mt-4">
                                <a href="{% url 'lecturer_dashboard' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let video = document.getElementById('video');
        let canvas = document.getElementById('canvas');
        let context = canvas.getContext('2d');
        let stream = null;
        let recognitionInProgress = false;

        // Clear captured image
        function clearCapturedImage() {
            const capturedImageDiv = document.getElementById('capturedImageDiv');
            if (capturedImageDiv) {
                capturedImageDiv.style.display = 'none';
            }
        }

        // Start camera
        document.getElementById('startCamera').addEventListener('click', async function() {
            try {
                // Clear any previous captured image
                clearCapturedImage();
                
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { width: 640, height: 480 } 
                });
                video.srcObject = stream;
                
                // Show video, hide placeholder
                document.getElementById('cameraPlaceholder').style.display = 'none';
                video.classList.remove('d-none');
                
                // Update button states
                this.disabled = true;
                document.getElementById('markAttendance').disabled = false;
                document.getElementById('stopCamera').disabled = false;
                
                showStatus('Camera started successfully! Students can now mark attendance.', 'success');
            } catch (error) {
                console.error('Error accessing camera:', error);
                showStatus('Error accessing camera. Please check permissions and try again.', 'danger');
            }
        });

        // Stop camera
        document.getElementById('stopCamera').addEventListener('click', function() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                video.srcObject = null;
                stream = null;
            }
            
            // Clear captured image
            clearCapturedImage();
            
            // Show placeholder, hide video
            video.classList.add('d-none');
            document.getElementById('cameraPlaceholder').style.display = 'flex';
            
            // Update button states
            document.getElementById('startCamera').disabled = false;
            document.getElementById('markAttendance').disabled = true;
            this.disabled = true;
            
            showStatus('Camera stopped.', 'info');
        });

        // Mark attendance (for any student)
        document.getElementById('markAttendance').addEventListener('click', async function() {
            if (recognitionInProgress) return;
            
            recognitionInProgress = true;
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-camera me-2"></i>Capturing...';
            this.disabled = true;
            
            try {
                // Step 1: Capture and display the image immediately
                showStatus('📸 Capturing student image...', 'info');
                
                // Set canvas size to match video
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                
                // Capture current frame
                context.drawImage(video, 0, 0, canvas.width, canvas.height);
                const imageData = canvas.toDataURL('image/jpeg', 0.8);
                
                // Step 2: Show captured image in UI
                showCapturedImage(imageData);
                
                // Step 3: Update button to show processing
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                showStatus('🔍 Processing image for recognition...', 'info');
                
                // Step 4: Send to recognition API
                const response = await fetch('{% url "api_recognize_face" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify({
                        image_data: imageData,
                        class_id: {{ class.id }}
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus(`✅ ${result.message}`, 'success');
                    
                    // Refresh the page to update student list
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else if (result.already_marked) {
                    // Handle already marked attendance
                    showStatus(`ℹ️ ${result.message}`, 'info');
                } else {
                    showStatus(`❌ ${result.message}`, 'warning');
                }
            } catch (error) {
                console.error('Error marking attendance:', error);
                showStatus('Error processing request. Please try again.', 'danger');
            } finally {
                recognitionInProgress = false;
                this.innerHTML = originalText;
                this.disabled = false;
            }
        });

        // Show captured image in UI
        function showCapturedImage(imageData) {
            // Create or update captured image display
            let capturedImageDiv = document.getElementById('capturedImageDiv');
            
            if (!capturedImageDiv) {
                // Create the captured image section
                capturedImageDiv = document.createElement('div');
                capturedImageDiv.id = 'capturedImageDiv';
                capturedImageDiv.className = 'mt-4';
                capturedImageDiv.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-image me-2"></i>Captured Image
                            </h6>
                        </div>
                        <div class="card-body text-center">
                            <img id="capturedImage" class="img-fluid rounded" style="max-height: 200px; border: 2px solid #28a745;">
                            <p class="mt-2 mb-0 text-success">
                                <i class="fas fa-check-circle me-1"></i>Image captured successfully!
                            </p>
                        </div>
                    </div>
                `;
                
                // Insert after the camera section
                const cameraSection = document.querySelector('.camera-section');
                cameraSection.appendChild(capturedImageDiv);
            }
            
            // Set the captured image
            document.getElementById('capturedImage').src = imageData;
            
            // Show the captured image section
            capturedImageDiv.style.display = 'block';
        }

        // Show status message
        function showStatus(message, type) {
            const statusDiv = document.getElementById('recognitionStatus');
            statusDiv.className = `alert alert-${type}`;
            statusDiv.innerHTML = message;
            statusDiv.style.display = 'block';
            
            // Auto-hide after 10 seconds for non-success messages
            if (type !== 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 10000);
            }
        }
    </script>
</body>
</html>
