{% extends 'attendance/base.html' %}
{% load static %}

{% block title %}Classes - AI Attendance System{% endblock %}

{% block page_header %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="h2 mb-4">
                <i class="fas fa-chalkboard me-2"></i>Classes
            </h1>
            <a href="/admin/attendance/class/add/" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Add New Class
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    {% if classes %}
        {% for class in classes %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{{ class.code }}</h5>
                    <span class="badge bg-primary">{{ class.students.count }} students</span>
                </div>
                <div class="card-body">
                    <h6 class="card-subtitle mb-2 text-muted">{{ class.name }}</h6>
                    {% if class.description %}
                        <p class="card-text">{{ class.description|truncatewords:15 }}</p>
                    {% endif %}
                    
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-user-tie me-1"></i>
                            Instructor: {{ class.instructor.get_full_name|default:class.instructor.username }}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Created: {{ class.created_at|date:"M d, Y" }}
                        </small>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="d-grid gap-2">
                        <a href="{% url 'take_attendance' class.id %}" class="btn btn-success">
                            <i class="fas fa-user-check me-1"></i>Take Attendance
                        </a>
                        <div class="btn-group" role="group">
                            <a href="{% url 'class_detail' class.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            <a href="{% url 'class_attendance_history' class.id %}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-history me-1"></i>History
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-chalkboard fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No classes found</h5>
                    <p class="text-muted">Create your first class to start taking attendance.</p>
                    <a href="/admin/attendance/class/add/" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Create First Class
                    </a>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- Quick Stats -->
{% if classes %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Quick Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-primary">{{ classes|length }}</h4>
                            <p class="text-muted mb-0">Total Classes</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-success" id="totalStudents">-</h4>
                            <p class="text-muted mb-0">Total Enrollments</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-info" id="avgStudents">-</h4>
                            <p class="text-muted mb-0">Avg Students/Class</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">{{ classes|length }}</h4>
                        <p class="text-muted mb-0">Active Classes</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Calculate statistics
document.addEventListener('DOMContentLoaded', function() {
    const studentCounts = [];
    let totalStudents = 0;
    
    // Extract student counts from badges
    document.querySelectorAll('.badge.bg-primary').forEach(badge => {
        const count = parseInt(badge.textContent.match(/\d+/)[0]);
        studentCounts.push(count);
        totalStudents += count;
    });
    
    // Calculate average
    const avgStudents = studentCounts.length > 0 ? 
        Math.round(totalStudents / studentCounts.length) : 0;
    
    // Update statistics
    document.getElementById('totalStudents').textContent = totalStudents;
    document.getElementById('avgStudents').textContent = avgStudents;
});

// Add hover effects to cards
document.querySelectorAll('.card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px)';
        this.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
    });
});
</script>
{% endblock %}
