/* AI Attendance System Custom Styles */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

/* Camera and Video Styles */
#video {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    background-color: #000;
}

#capturedImage {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Status and Alert Styles */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-card .stats-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* Present Students List */
.present-student-item {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease-in-out;
}

.present-student-item:hover {
    background-color: rgba(40, 167, 69, 0.15);
    transform: translateX(5px);
}

/* Recognition Status */
.recognition-status {
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    font-weight: 500;
}

.recognition-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.recognition-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.recognition-error {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background-color: #495057;
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: background-color 0.2s ease-in-out;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badge Styles */
.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
}

/* Breadcrumb Styles */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 20px;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    font-size: 1.2rem;
    color: #6c757d;
}

/* Form Styles */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.2s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 20px;
    }
    
    #video {
        width: 100%;
        height: auto;
    }
    
    .btn-lg {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Success/Error Indicators */
.success-indicator {
    color: #28a745;
    font-weight: bold;
}

.error-indicator {
    color: #dc3545;
    font-weight: bold;
}

.confidence-high {
    color: #28a745;
}

.confidence-medium {
    color: #ffc107;
}

.confidence-low {
    color: #dc3545;
}

/* Footer */
footer {
    margin-top: auto;
    background-color: #f8f9fa !important;
    border-top: 1px solid #e9ecef;
}
