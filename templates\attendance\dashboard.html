{% extends 'attendance/base.html' %}
{% load static %}

{% block title %}Dashboard - AI Attendance System{% endblock %}

{% block page_header %}
<div class="row">
    <div class="col-12">
        <h1 class="h2 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
        </h1>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_students }}</h4>
                        <p class="card-text">Total Students</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_classes }}</h4>
                        <p class="card-text">Active Classes</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chalkboard fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ recent_sessions|length }}</h4>
                        <p class="card-text">Recent Sessions</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">Today</h4>
                        <p class="card-text">Current Date</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'student_register' %}" class="btn btn-primary w-100">
                            <i class="fas fa-user-plus me-2"></i>Register Student
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'class_list' %}" class="btn btn-success w-100">
                            <i class="fas fa-chalkboard-teacher me-2"></i>View Classes
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'attendance_history' %}" class="btn btn-info w-100">
                            <i class="fas fa-history me-2"></i>View History
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="/admin/" class="btn btn-secondary w-100">
                            <i class="fas fa-cog me-2"></i>Admin Panel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Active Classes -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chalkboard me-2"></i>Active Classes
                </h5>
            </div>
            <div class="card-body">
                {% if classes %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Instructor</th>
                                    <th>Students</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for class in classes %}
                                <tr>
                                    <td><strong>{{ class.code }}</strong></td>
                                    <td>{{ class.name }}</td>
                                    <td>{{ class.instructor.get_full_name|default:class.instructor.username }}</td>
                                    <td>{{ class.students.count }}</td>
                                    <td>
                                        <a href="{% url 'take_attendance' class.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-user-check"></i> Take Attendance
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No active classes found.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Sessions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Sessions
                </h5>
            </div>
            <div class="card-body">
                {% if recent_sessions %}
                    {% for session in recent_sessions %}
                    <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                        <div>
                            <strong>{{ session.class_session.code }}</strong><br>
                            <small class="text-muted">{{ session.date }}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-success">{{ session.present_count }}/{{ session.total_students }}</span><br>
                            <small class="text-muted">{{ session.attendance_rate }}%</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No recent sessions.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
