{% extends 'attendance/base.html' %}
{% load static %}

{% block title %}Register Student - AI Attendance System{% endblock %}

{% block page_header %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'student_list' %}">Students</a></li>
                <li class="breadcrumb-item active">Register Student</li>
            </ol>
        </nav>
        <h1 class="h2 mb-4">
            <i class="fas fa-user-plus me-2"></i>Register New Student
        </h1>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Student Information
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="student_id" class="form-label">Student ID *</label>
                                <input type="text" class="form-control" id="student_id" name="student_id" required>
                                <div class="form-text">Unique identifier for the student</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" name="phone">
                        <div class="form-text">Optional contact number</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'student_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Register Student
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Next Steps
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-3">After registering the student, you will need to:</p>
                <ol>
                    <li class="mb-2">
                        <strong>Enroll Face Images</strong><br>
                        <small class="text-muted">Capture and register the student's face for recognition</small>
                    </li>
                    <li class="mb-2">
                        <strong>Assign to Classes</strong><br>
                        <small class="text-muted">Enroll the student in relevant classes</small>
                    </li>
                    <li class="mb-2">
                        <strong>Test Recognition</strong><br>
                        <small class="text-muted">Verify that face recognition works correctly</small>
                    </li>
                </ol>
                
                <div class="alert alert-info mt-3">
                    <small>
                        <i class="fas fa-lightbulb me-1"></i>
                        <strong>Tip:</strong> Make sure to have good quality photos for better recognition accuracy.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const studentId = document.getElementById('student_id').value.trim();
    const email = document.getElementById('email').value.trim();
    const firstName = document.getElementById('first_name').value.trim();
    const lastName = document.getElementById('last_name').value.trim();
    
    if (!studentId || !email || !firstName || !lastName) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return;
    }
    
    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Registering...';
});

// Auto-format student ID
document.getElementById('student_id').addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
});
</script>
{% endblock %}
