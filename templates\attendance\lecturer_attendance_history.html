<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance History - AI Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            border-left: 4px solid #3498db;
        }

        .main-content {
            background: white;
            min-height: 100vh;
            border-radius: 20px 0 0 20px;
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .filter-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table td {
            vertical-align: middle;
            border-color: #e9ecef;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .btn-filter {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            border-radius: 10px;
            padding: 8px 20px;
            transition: all 0.3s ease;
        }

        .btn-filter:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
            transform: translateY(-2px);
        }

        .btn-clear {
            background: #6c757d;
            border: none;
            color: white;
            border-radius: 10px;
            padding: 8px 20px;
            transition: all 0.3s ease;
        }

        .btn-clear:hover {
            background: #5a6268;
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-4 text-center border-bottom border-secondary">
                        <i class="fas fa-user-tie fa-3x text-primary mb-3"></i>
                        <h5 class="text-white mb-0">Lecturer Portal</h5>
                        <small class="text-muted">{{ lecturer.get_full_name|default:lecturer.username }}</small>
                    </div>

                    <nav class="nav flex-column">
                        <a class="nav-link" href="{% url 'lecturer_dashboard' %}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="#classes">
                            <i class="fas fa-chalkboard me-2"></i>My Classes
                        </a>
                        <a class="nav-link active" href="{% url 'lecturer_attendance_history' %}">
                            <i class="fas fa-calendar-check me-2"></i>Attendance History
                        </a>
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#profileModal">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <div class="border-top border-secondary mt-3 pt-3">
                            <a class="nav-link" href="{% url 'student_logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 px-0">
                <div class="main-content p-4">
                    <!-- Messages -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 class="mb-1">
                                <i class="fas fa-calendar-check me-2 text-primary"></i>Attendance History
                            </h2>
                            <p class="text-muted mb-0">View and manage attendance records across all your classes</p>
                        </div>
                        <div class="text-end">
                            <h4 class="mb-0">{{ today|date:"M d, Y" }}</h4>
                            <small class="text-muted">{{ today|date:"l" }}</small>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h3 class="mb-0">{{ total_records }}</h3>
                                        <p class="mb-0">Total Records</p>
                                    </div>
                                    <i class="fas fa-list fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h3 class="mb-0">{{ present_count }}</h3>
                                        <p class="mb-0">Present</p>
                                    </div>
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                        {% comment %} <div class="col-md-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h3 class="mb-0">{{ late_count }}</h3>
                                        <p class="mb-0">Late</p>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div> {% endcomment %}
                        <div class="col-md-4">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h3 class="mb-0">{{ attendance_rate }}%</h3>
                                        <p class="mb-0">Attendance Rate</p>
                                    </div>
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter Form -->
                    <div class="filter-card">
                        <form method="GET" class="row g-3">
                            <div class="col-md-2">
                                <label for="search" class="form-label">
                                    <i class="fas fa-search me-1"></i>Search
                                </label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="{{ search_query }}" placeholder="Student or class...">
                            </div>
                            <div class="col-md-2">
                                <label for="class_filter" class="form-label">
                                    <i class="fas fa-chalkboard me-1"></i>Class
                                </label>
                                <select class="form-select" id="class_filter" name="class_filter">
                                    <option value="">All Classes</option>
                                    {% for class in lecturer_classes %}
                                        <option value="{{ class.id }}" {% if class_filter == class.id|stringformat:"s" %}selected{% endif %}>
                                            {{ class.code }} - {{ class.name|truncatechars:15 }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="student_filter" class="form-label">
                                    <i class="fas fa-user me-1"></i>Student
                                </label>
                                <select class="form-select" id="student_filter" name="student_filter">
                                    <option value="">All Students</option>
                                    {% for student in lecturer_students %}
                                        <option value="{{ student.id }}" {% if student_filter == student.id|stringformat:"s" %}selected{% endif %}>
                                            {{ student.user.get_full_name|default:student.user.username|truncatechars:20 }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="department_filter" class="form-label">
                                    <i class="fas fa-building me-1"></i>Department
                                </label>
                                <select class="form-select" id="department_filter" name="department_filter">
                                    <option value="">All Departments</option>
                                    {% for department in departments %}
                                        <option value="{{ department.id }}" {% if department_filter == department.id|stringformat:"s" %}selected{% endif %}>
                                            {{ department.name|truncatechars:20 }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label for="level_filter" class="form-label">
                                    <i class="fas fa-layer-group me-1"></i>Level
                                </label>
                                <select class="form-select" id="level_filter" name="level_filter">
                                    <option value="">All</option>
                                    {% for level in levels %}
                                        <option value="{{ level.id }}" {% if level_filter == level.id|stringformat:"s" %}selected{% endif %}>
                                            {{ level.code }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label for="semester_filter" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>Semester
                                </label>
                                <select class="form-select" id="semester_filter" name="semester_filter">
                                    <option value="">All</option>
                                    {% for value, label in semester_choices %}
                                        <option value="{{ value }}" {% if semester_filter == value %}selected{% endif %}>
                                            {{ value }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </form>

                        <!-- Second row for remaining filters -->
                        <form method="GET" class="row g-3 mt-2">
                            <!-- Hidden fields to preserve other filters -->
                            <input type="hidden" name="search" value="{{ search_query }}">
                            <input type="hidden" name="class_filter" value="{{ class_filter }}">
                            <input type="hidden" name="student_filter" value="{{ student_filter }}">
                            <input type="hidden" name="department_filter" value="{{ department_filter }}">
                            <input type="hidden" name="level_filter" value="{{ level_filter }}">
                            <input type="hidden" name="semester_filter" value="{{ semester_filter }}">

                            <div class="col-md-1">
                                <label for="status_filter" class="form-label">
                                    <i class="fas fa-flag me-1"></i>Status
                                </label>
                                <select class="form-select" id="status_filter" name="status_filter">
                                    <option value="">All</option>
                                    <option value="present" {% if status_filter == 'present' %}selected{% endif %}>Present</option>
                                    <option value="late" {% if status_filter == 'late' %}selected{% endif %}>Late</option>
                                    <option value="absent" {% if status_filter == 'absent' %}selected{% endif %}>Absent</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>From Date
                                </label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>To Date
                                </label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <div class="d-grid w-100">
                                    <button type="submit" class="btn btn-filter">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        {% if search_query or class_filter or status_filter or date_from or date_to or student_filter or department_filter or level_filter or semester_filter %}
                        <div class="mt-3">
                            <a href="{% url 'lecturer_attendance_history' %}" class="btn btn-clear btn-sm">
                                <i class="fas fa-times me-1"></i>Clear Filters
                            </a>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Attendance Records Table -->
                    <div class="table-container">
                        {% if attendance_records %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th><i class="fas fa-user me-2"></i>Student</th>
                                            <th><i class="fas fa-building me-2"></i>Department</th>
                                            <th><i class="fas fa-layer-group me-2"></i>Level</th>
                                            <th><i class="fas fa-chalkboard me-2"></i>Class</th>
                                            <th><i class="fas fa-calendar me-2"></i>Date</th>
                                            <th><i class="fas fa-clock me-2"></i>Time</th>
                                            <th><i class="fas fa-flag me-2"></i>Status</th>
                                            <th><i class="fas fa-percentage me-2"></i>Confidence</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for record in attendance_records %}
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>{{ record.student.user.get_full_name|default:record.student.user.username }}</strong><br>
                                                    <small class="text-muted">{{ record.student.student_id }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ record.class_attended.code }}</strong><br>
                                                    <small class="text-muted">{{ record.class_attended.name|truncatechars:25 }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold">{{ record.date|date:"M d, Y" }}</span><br>
                                                <small class="text-muted">{{ record.date|date:"l" }}</small>
                                            </td>
                                            <td>
                                                <i class="fas fa-clock text-muted me-1"></i>
                                                {{ record.timestamp|time:"H:i" }}
                                            </td>
                                            <td>
                                                {% if record.status == 'present' %}
                                                    <span class="status-badge bg-success text-white">
                                                        <i class="fas fa-check me-1"></i>Present
                                                    </span>
                                                {% elif record.status == 'late' %}
                                                    <span class="status-badge bg-warning text-dark">
                                                        <i class="fas fa-clock me-1"></i>Late
                                                    </span>
                                                {% else %}
                                                    <span class="status-badge bg-danger text-white">
                                                        <i class="fas fa-times me-1"></i>Absent
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if record.recognition_confidence %}
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress me-2" style="width: 60px; height: 8px;">
                                                            <div class="progress-bar bg-success"
                                                                 style="width: {{ record.recognition_confidence }}%"></div>
                                                        </div>
                                                        <small>{{ record.recognition_confidence|floatformat:1 }}%</small>
                                                    </div>
                                                {% else %}
                                                    <small class="text-muted">N/A</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if record.notes %}
                                                    <small>{{ record.notes|truncatechars:30 }}</small>
                                                {% else %}
                                                    <small class="text-muted">-</small>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-4x text-muted mb-4"></i>
                                <h4 class="text-muted mb-3">No Attendance Records Found</h4>
                                {% if search_query or class_filter or status_filter or date_from or date_to or student_filter %}
                                    <p class="text-muted mb-4">No records match your current filters.</p>
                                    <a href="{% url 'lecturer_attendance_history' %}" class="btn btn-primary">
                                        <i class="fas fa-refresh me-2"></i>Clear Filters
                                    </a>
                                {% else %}
                                    <p class="text-muted mb-4">No attendance records found for your classes.</p>
                                    <a href="{% url 'lecturer_dashboard' %}" class="btn btn-primary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                    </a>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div class="modal fade" id="profileModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-tie me-2"></i>Lecturer Profile
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12 text-center mb-4">
                            <i class="fas fa-user-tie fa-4x text-primary mb-3"></i>
                            <h4>{{ lecturer.get_full_name|default:lecturer.username }}</h4>
                            <p class="text-muted">Lecturer Profile</p>
                        </div>
                    </div>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong><i class="fas fa-id-card me-2"></i>Username:</strong></td>
                            <td>{{ lecturer.username }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-user me-2"></i>Name:</strong></td>
                            <td>{{ lecturer.get_full_name|default:lecturer.username }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-envelope me-2"></i>Email:</strong></td>
                            <td>{{ lecturer.email }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-phone me-2"></i>Phone:</strong></td>
                            <td>{{ lecturer.phone|default:"Not provided" }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-chalkboard me-2"></i>Classes Teaching:</strong></td>
                            <td>{{ lecturer_classes|length }}</td>
                        </tr>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
