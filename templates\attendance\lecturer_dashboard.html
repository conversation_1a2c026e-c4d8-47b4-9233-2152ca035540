<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecturer Dashboard - AI Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            border-left: 4px solid #3498db;
        }
        
        .main-content {
            background: white;
            min-height: 100vh;
            border-radius: 20px 0 0 20px;
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .class-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        
        .class-card:hover {
            transform: translateY(-5px);
        }
        
        .btn-attendance {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-attendance:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .attendance-badge {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-4 text-center border-bottom border-secondary">
                        <i class="fas fa-chalkboard-teacher fa-3x text-primary mb-3"></i>
                        <h5 class="text-white mb-0">Lecturer Portal</h5>
                        <small class="text-muted">{{ lecturer.get_full_name|default:lecturer.username }}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="{% url 'lecturer_dashboard' %}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="#classes">
                            <i class="fas fa-chalkboard me-2"></i>My Classes
                        </a>
                        <a class="nav-link" href="{% url 'lecturer_attendance_history' %}">
                            <i class="fas fa-calendar-check me-2"></i>Attendance History
                        </a>
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#profileModal">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <div class="border-top border-secondary mt-3 pt-3">
                            <a class="nav-link" href="{% url 'student_logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 px-0">
                <div class="main-content p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 class="mb-1">
                                <i class="fas fa-sun me-2 text-warning"></i>Good day, {{ lecturer.get_full_name|default:lecturer.username }}!
                            </h2>
                            <p class="text-muted mb-0">Manage your classes and track student attendance</p>
                        </div>
                        <div class="text-end">
                            <h4 class="mb-0">{{ today|date:"M d, Y" }}</h4>
                            <small class="text-muted">{{ today|date:"l" }}</small>
                        </div>
                    </div>

                    <!-- Messages -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h3 class="mb-0">{{ total_classes }}</h3>
                                        <p class="mb-0">Active Classes</p>
                                    </div>
                                    <i class="fas fa-chalkboard fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h3 class="mb-0">{{ total_students }}</h3>
                                        <p class="mb-0">Total Students</p>
                                    </div>
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h3 class="mb-0">{{ today_attendance_count }}</h3>
                                        <p class="mb-0">Today's Attendance</p>
                                    </div>
                                    <i class="fas fa-user-check fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- My Classes Section -->
                    <div class="row">
                        <div class="col-12">
                            <h3 class="mb-3" id="classes">
                                <i class="fas fa-chalkboard me-2"></i>My Classes
                            </h3>
                        </div>
                    </div>

                    {% if lecturer_classes %}
                        <div class="row">
                            {% for class in lecturer_classes %}
                                <div class="col-md-6 col-lg-4">
                                    <div class="card class-card">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <h5 class="card-title mb-0">{{ class.code }}</h5>
                                                <span class="attendance-badge">
                                                    <i class="fas fa-users me-1"></i>{{ class.students.count }}
                                                </span>
                                            </div>

                                            <h6 class="card-subtitle mb-3 text-muted">{{ class.name }}</h6>

                                            {% if class.description %}
                                            <div class="mb-3">
                                                <p class="text-muted small mb-0">{{ class.description|truncatewords:15 }}</p>
                                            </div>
                                            {% endif %}

                                            <div class="row text-center mb-3">
                                                <div class="col-6">
                                                    <div class="border-end">
                                                        <h6 class="mb-0 text-primary">{{ class.students.count }}</h6>
                                                        <small class="text-muted">Students</small>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <h6 class="mb-0 text-success">{{ class.attendance_today_count|default:0 }}</h6>
                                                    <small class="text-muted">Present Today</small>
                                                </div>
                                            </div>

                                            <div class="d-grid">
                                                <a href="{% url 'lecturer_attendance_session' class.id %}"
                                                   class="btn btn-attendance">
                                                    <i class="fas fa-camera me-2"></i>Start Attendance Session
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-chalkboard fa-3x text-muted mb-4"></i>
                            <h4 class="text-muted mb-3">No Classes Assigned</h4>
                            <p class="text-muted">You don't have any classes assigned yet. Please contact your department head or system administrator.</p>
                        </div>
                    {% endif %}

                    <!-- Recent Attendance Section -->
                    {% if recent_attendance %}
                        <div class="row mt-5">
                            <div class="col-12">
                                <h3 class="mb-3">
                                    <i class="fas fa-history me-2"></i>Recent Attendance
                                </h3>
                                <div class="card">
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Student</th>
                                                        <th>Class</th>
                                                        <th>Date</th>
                                                        <th>Time</th>
                                                        <th>Status</th>
                                                        <th>Confidence</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for attendance in recent_attendance %}
                                                        <tr>
                                                            <td>
                                                                <strong>{{ attendance.student.full_name }}</strong><br>
                                                                <small class="text-muted">{{ attendance.student.student_id }}</small>
                                                            </td>
                                                            <td>{{ attendance.class_attended.code }}</td>
                                                            <td>{{ attendance.date }}</td>
                                                            <td>{{ attendance.timestamp|time:"H:i" }}</td>
                                                            <td>
                                                                {% if attendance.status == 'present' %}
                                                                    <span class="badge bg-success">Present</span>
                                                                {% elif attendance.status == 'late' %}
                                                                    <span class="badge bg-warning">Late</span>
                                                                {% else %}
                                                                    <span class="badge bg-danger">{{ attendance.status|title }}</span>
                                                                {% endif %}
                                                            </td>
                                                            <td>
                                                                {% if attendance.recognition_confidence %}
                                                                    {{ attendance.recognition_confidence|floatformat:0 }}%
                                                                {% else %}
                                                                    -
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div class="modal fade" id="profileModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-tie me-2"></i>Lecturer Profile
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12 text-center mb-4">
                            <i class="fas fa-user-tie fa-4x text-primary mb-3"></i>
                            <h4>{{ lecturer.get_full_name|default:lecturer.username }}</h4>
                            <p class="text-muted">Lecturer Profile</p>
                        </div>
                    </div>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong><i class="fas fa-id-card me-2"></i>Username:</strong></td>
                            <td>{{ lecturer.username }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-user me-2"></i>Name:</strong></td>
                            <td>{{ lecturer.get_full_name|default:lecturer.username }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-envelope me-2"></i>Email:</strong></td>
                            <td>{{ lecturer.email }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-phone me-2"></i>Phone:</strong></td>
                            <td>{{ lecturer.phone|default:"Not provided" }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-chalkboard me-2"></i>Classes Teaching:</strong></td>
                            <td>{{ total_classes }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-users me-2"></i>Total Students:</strong></td>
                            <td>{{ total_students }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-calendar-check me-2"></i>Today's Attendance:</strong></td>
                            <td>{{ today_attendance_count }}</td>
                        </tr>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
