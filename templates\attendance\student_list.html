{% extends 'attendance/base.html' %}
{% load static %}

{% block title %}Students - AI Attendance System{% endblock %}

{% block page_header %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="h2 mb-4">
                <i class="fas fa-users me-2"></i>Students
            </h1>
            <a href="{% url 'student_register' %}" class="btn btn-primary">
                <i class="fas fa-user-plus me-1"></i>Register New Student
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>All Students
                </h5>
            </div>
            <div class="card-body">
                {% if students %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Student ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Classes</th>
                                    <th>Face Encodings</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for student in students %}
                                <tr>
                                    <td><strong>{{ student.student_id }}</strong></td>
                                    <td>{{ student.full_name }}</td>
                                    <td>{{ student.email }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ student.enrolled_classes.count }}</span>
                                    </td>
                                    <td>
                                        {% with encodings_count=student.face_encodings.count %}
                                            {% if encodings_count > 0 %}
                                                <span class="badge bg-success">{{ encodings_count }} enrolled</span>
                                            {% else %}
                                                <span class="badge bg-warning">Not enrolled</span>
                                            {% endif %}
                                        {% endwith %}
                                    </td>
                                    <td>
                                        {% if student.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'student_detail' student.student_id %}" 
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'face_enrollment' student.student_id %}" 
                                               class="btn btn-sm btn-outline-success" title="Enroll Face">
                                                <i class="fas fa-camera"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Students pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">First</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No students found</h5>
                        <p class="text-muted">Get started by registering your first student.</p>
                        <a href="{% url 'student_register' %}" class="btn btn-primary">
                            <i class="fas fa-user-plus me-1"></i>Register First Student
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
{% if students %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>{{ page_obj.paginator.count }}</h4>
                <p class="mb-0">Total Students</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-2x mb-2"></i>
                <h4>{{ students|length }}</h4>
                <p class="mb-0">Active Students</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-camera fa-2x mb-2"></i>
                <h4 id="enrolledCount">-</h4>
                <p class="mb-0">Face Enrolled</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4 id="notEnrolledCount">-</h4>
                <p class="mb-0">Not Enrolled</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Calculate face enrollment statistics
document.addEventListener('DOMContentLoaded', function() {
    let enrolledCount = 0;
    let notEnrolledCount = 0;
    
    // Count students with and without face encodings
    const badges = document.querySelectorAll('td .badge');
    badges.forEach(badge => {
        if (badge.textContent.includes('enrolled') && badge.classList.contains('bg-success')) {
            enrolledCount++;
        } else if (badge.textContent.includes('Not enrolled')) {
            notEnrolledCount++;
        }
    });
    
    // Update the statistics cards
    document.getElementById('enrolledCount').textContent = enrolledCount;
    document.getElementById('notEnrolledCount').textContent = notEnrolledCount;
});

// Add hover effects to table rows
document.querySelectorAll('tbody tr').forEach(row => {
    row.addEventListener('mouseenter', function() {
        this.style.backgroundColor = '#f8f9fa';
    });
    
    row.addEventListener('mouseleave', function() {
        this.style.backgroundColor = '';
    });
});
</script>
{% endblock %}
