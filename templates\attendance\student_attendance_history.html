<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance History - AI Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            border-left: 4px solid #3498db;
        }

        .main-content {
            background: white;
            min-height: 100vh;
            border-radius: 20px 0 0 20px;
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .filter-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table td {
            vertical-align: middle;
            border-color: #e9ecef;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .btn-filter {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            border-radius: 10px;
            padding: 8px 20px;
            transition: all 0.3s ease;
        }

        .btn-filter:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
            transform: translateY(-2px);
        }

        .btn-clear {
            background: #6c757d;
            border: none;
            color: white;
            border-radius: 10px;
            padding: 8px 20px;
            transition: all 0.3s ease;
        }

        .btn-clear:hover {
            background: #5a6268;
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-4 text-center border-bottom border-secondary">
                        <i class="fas fa-user-graduate fa-3x text-primary mb-3"></i>
                        <h5 class="text-white mb-0">Student Portal</h5>
                        <small class="text-muted">{{ student.get_full_name|default:user.username }}</small>
                    </div>

                    <nav class="nav flex-column">
                        <a class="nav-link" href="{% url 'student_dashboard' %}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="#classes">
                            <i class="fas fa-chalkboard me-2"></i>My Classes
                        </a>
                        <a class="nav-link active" href="{% url 'student_attendance_history' %}">
                            <i class="fas fa-calendar-check me-2"></i>Attendance
                        </a>
                        <a class="nav-link" href="{% url 'student_face_enrollment' %}">
                            <i class="fas fa-camera me-2"></i>Face Registration
                        </a>
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#profileModal">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <div class="border-top border-secondary mt-3 pt-3">
                            <a class="nav-link" href="{% url 'student_logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 px-0">
                <div class="main-content p-4">
                    <!-- Messages -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 class="mb-1">
                                <i class="fas fa-calendar-check me-2 text-primary"></i>Attendance History
                            </h2>
                            <p class="text-muted mb-0">View and search your attendance records</p>
                        </div>
                        <div class="text-end">
                            <h4 class="mb-0">{{ today|date:"M d, Y" }}</h4>
                            <small class="text-muted">{{ today|date:"l" }}</small>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h3 class="mb-0">{{ total_records }}</h3>
                                        <p class="mb-0">Total Records</p>
                                    </div>
                                    <i class="fas fa-list fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h3 class="mb-0">{{ present_count }}</h3>
                                        <p class="mb-0">Present</p>
                                    </div>
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h3 class="mb-0">{{ attendance_rate }}%</h3>
                                        <p class="mb-0">Attendance Rate</p>
                                    </div>
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter Form -->
                    <div class="filter-card">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">
                                    <i class="fas fa-search me-1"></i>Search Classes
                                </label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ search_query }}" placeholder="Class name or code...">
                            </div>
                            <div class="col-md-2">
                                <label for="class_filter" class="form-label">
                                    <i class="fas fa-filter me-1"></i>Class
                                </label>
                                <select class="form-select" id="class_filter" name="class_filter">
                                    <option value="">All Classes</option>
                                    {% for class in enrolled_classes %}
                                        <option value="{{ class.id }}" {% if class_filter == class.id|stringformat:"s" %}selected{% endif %}>
                                            {{ class.code }} - {{ class.name|truncatechars:20 }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="status_filter" class="form-label">
                                    <i class="fas fa-flag me-1"></i>Status
                                </label>
                                <select class="form-select" id="status_filter" name="status_filter">
                                    <option value="">All Status</option>
                                    <option value="present" {% if status_filter == 'present' %}selected{% endif %}>Present</option>
                                    <option value="late" {% if status_filter == 'late' %}selected{% endif %}>Late</option>
                                    <option value="absent" {% if status_filter == 'absent' %}selected{% endif %}>Absent</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>From Date
                                </label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>To Date
                                </label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <div class="d-grid w-100">
                                    <button type="submit" class="btn btn-filter">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        {% if search_query or class_filter or status_filter or date_from or date_to %}
                        <div class="mt-3">
                            <a href="{% url 'student_attendance_history' %}" class="btn btn-clear btn-sm">
                                <i class="fas fa-times me-1"></i>Clear Filters
                            </a>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Attendance Records Table -->
                    <div class="table-container">
                        {% if attendance_records %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th><i class="fas fa-chalkboard me-2"></i>Class</th>
                                            <th><i class="fas fa-calendar me-2"></i>Date</th>
                                            <th><i class="fas fa-clock me-2"></i>Time</th>
                                            <th><i class="fas fa-flag me-2"></i>Status</th>
                                            <th><i class="fas fa-percentage me-2"></i>Confidence</th>
                                            <th><i class="fas fa-sticky-note me-2"></i>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for record in attendance_records %}
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>{{ record.class_attended.code }}</strong><br>
                                                    <small class="text-muted">{{ record.class_attended.name }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold">{{ record.date|date:"M d, Y" }}</span><br>
                                                <small class="text-muted">{{ record.date|date:"l" }}</small>
                                            </td>
                                            <td>
                                                <i class="fas fa-clock text-muted me-1"></i>
                                                {{ record.timestamp|time:"H:i" }}
                                            </td>
                                            <td>
                                                {% if record.status == 'present' %}
                                                    <span class="status-badge bg-success text-white">
                                                        <i class="fas fa-check me-1"></i>Present
                                                    </span>
                                                {% elif record.status == 'late' %}
                                                    <span class="status-badge bg-warning text-dark">
                                                        <i class="fas fa-clock me-1"></i>Late
                                                    </span>
                                                {% else %}
                                                    <span class="status-badge bg-danger text-white">
                                                        <i class="fas fa-times me-1"></i>Absent
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if record.recognition_confidence %}
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress me-2" style="width: 60px; height: 8px;">
                                                            <div class="progress-bar bg-success"
                                                                 style="width: {{ record.recognition_confidence }}%"></div>
                                                        </div>
                                                        <small>{{ record.recognition_confidence|floatformat:1 }}%</small>
                                                    </div>
                                                {% else %}
                                                    <small class="text-muted">N/A</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if record.notes %}
                                                    <small>{{ record.notes|truncatechars:50 }}</small>
                                                {% else %}
                                                    <small class="text-muted">-</small>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-4x text-muted mb-4"></i>
                                <h4 class="text-muted mb-3">No Attendance Records Found</h4>
                                {% if search_query or class_filter or status_filter or date_from or date_to %}
                                    <p class="text-muted mb-4">No records match your current filters.</p>
                                    <a href="{% url 'student_attendance_history' %}" class="btn btn-primary">
                                        <i class="fas fa-refresh me-2"></i>Clear Filters
                                    </a>
                                {% else %}
                                    <p class="text-muted mb-4">You don't have any attendance records yet.</p>
                                    <a href="{% url 'student_dashboard' %}" class="btn btn-primary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                    </a>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal (same as student dashboard) -->
    <div class="modal fade" id="profileModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user me-2"></i>Student Profile
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12 text-center mb-4">
                            <i class="fas fa-user-graduate fa-4x text-primary mb-3"></i>
                            <h4>{{ student.get_full_name|default:user.username }}</h4>
                            <p class="text-muted">Student Profile</p>
                        </div>
                    </div>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong><i class="fas fa-id-card me-2"></i>Student ID:</strong></td>
                            <td>{{ student.student_id }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-user me-2"></i>Name:</strong></td>
                            <td>{{ student.get_full_name|default:user.username }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-envelope me-2"></i>Email:</strong></td>
                            <td>{{ student.email }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-phone me-2"></i>Phone:</strong></td>
                            <td>{{ student.phone|default:"Not provided" }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-chalkboard me-2"></i>Enrolled Classes:</strong></td>
                            <td>{{ enrolled_classes|length }}</td>
                        </tr>
                        <tr>
                            <td><strong><i class="fas fa-camera me-2"></i>Face Registration:</strong></td>
                            <td>
                                {% if student.face_encodings.count > 0 %}
                                    <span class="badge bg-success"><i class="fas fa-check me-1"></i>Registered</span>
                                {% else %}
                                    <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i>Not Registered</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="modal-footer">
                    {% if student.face_encodings.count == 0 %}
                    <a href="{% url 'student_face_enrollment' %}" class="btn btn-primary">
                        <i class="fas fa-camera me-2"></i>Register Face
                    </a>
                    {% endif %}
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
