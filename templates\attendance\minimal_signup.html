<!DOCTYPE html>
<html>
<head>
    <title>Minimal Signup Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>Minimal Signup Test</h3>
                    </div>
                    <div class="card-body">
                        <!-- Messages -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }}">{{ message }}</div>
                            {% endfor %}
                        {% endif %}
                        
                        <!-- NO JAVASCRIPT - Pure HTML Form -->
                        <form method="post" action="/signup/">
                            {% csrf_token %}
                            
                            <div class="mb-3">
                                <label class="form-label">Student ID</label>
                                <input type="text" class="form-control" name="student_id" value="MIN001" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-control" name="first_name" value="Minimal" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-control" name="last_name" value="Test" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" value="<EMAIL>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Phone</label>
                                <input type="text" class="form-control" name="phone" value="1234567890">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" name="password" value="password123" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" name="confirm_password" value="password123" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Submit (No JS)</button>
                        </form>
                        
                        <hr>
                        <p class="small text-muted">
                            This form has NO JavaScript validation - it should submit directly to the server.
                            If this doesn't work, the issue is on the server side.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
