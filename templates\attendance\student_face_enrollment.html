<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Registration - AI Attendance System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem 0;
        }
        
        .enrollment-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .enrollment-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .enrollment-body {
            padding: 2rem;
        }
        
        #video {
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            background-color: #000;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: transform 0.2s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: transform 0.2s ease;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: transform 0.2s ease;
        }
        
        .btn-warning:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, #e0a800 0%, #e8690b 100%);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .progress-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .step {
            display: flex;
            align-items: center;
            color: #6c757d;
        }
        
        .step.active {
            color: #667eea;
            font-weight: bold;
        }
        
        .step.completed {
            color: #28a745;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            font-weight: bold;
        }
        
        .step.active .step-number {
            background: #667eea;
            color: white;
        }
        
        .step.completed .step-number {
            background: #28a745;
            color: white;
        }
        
        .step-connector {
            width: 50px;
            height: 2px;
            background: #e9ecef;
            margin: 0 1rem;
        }
        
        .step.completed + .step-connector {
            background: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10 col-lg-8">
                <div class="enrollment-card">
                    <div class="enrollment-header">
                        <i class="fas fa-camera fa-3x mb-3"></i>
                        <h2 class="mb-2">Face Registration</h2>
                        <h4 class="mb-0 opacity-75">{{ student.full_name }}</h4>
                        <p class="mb-0 opacity-75">Complete your account setup</p>
                    </div>
                    
                    <div class="enrollment-body">
                        <!-- Progress Steps -->
                        <div class="progress-steps">
                            <div class="step completed">
                                <div class="step-number">
                                    <i class="fas fa-check"></i>
                                </div>
                                <span>Account Created</span>
                            </div>
                            <div class="step-connector"></div>
                            <div class="step active">
                                <div class="step-number">2</div>
                                <span>Face Registration</span>
                            </div>
                            <div class="step-connector"></div>
                            <div class="step">
                                <div class="step-number">3</div>
                                <span>Ready to Use</span>
                            </div>
                        </div>
                        
                        <!-- Messages -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Camera Section -->
                                <div class="text-center mb-4">
                                    <video id="video" width="100%" height="400" autoplay class="d-none"></video>
                                    <div id="cameraPlaceholder" class="d-flex align-items-center justify-content-center" 
                                         style="width: 100%; height: 400px; background: #f8f9fa; border-radius: 15px; border: 2px dashed #dee2e6;">
                                        <div class="text-center">
                                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">Click "Start Camera" to begin face registration</p>
                                        </div>
                                    </div>
                                    <canvas id="canvas" style="display: none;"></canvas>
                                </div>
                                
                                <!-- Captured Image Preview -->
                                <div id="imagePreview" class="text-center mb-4" style="display: none;">
                                    <h6>Captured Image:</h6>
                                    <img id="capturedImage" class="border rounded" style="max-width: 300px;">
                                </div>
                                
                                <!-- Controls -->
                                <div class="text-center mb-4">
                                    <button id="startCamera" class="btn btn-primary btn-lg me-2">
                                        <i class="fas fa-video me-2"></i>Start Camera
                                    </button>
                                    <button id="captureImage" class="btn btn-success btn-lg me-2" disabled>
                                        <i class="fas fa-camera me-2"></i>Capture Face
                                    </button>
                                    <button id="retakeImage" class="btn btn-warning btn-lg me-2" style="display: none;">
                                        <i class="fas fa-redo me-2"></i>Retake
                                    </button>
                                </div>
                                
                                <!-- Validation Status -->
                                <div id="validationStatus" class="alert" style="display: none;"></div>
                                
                                <!-- Enrollment Form -->
                                <form id="enrollmentForm" method="post" style="display: none;">
                                    {% csrf_token %}
                                    <input type="hidden" id="imageData" name="image_data">
                                    <input type="hidden" name="is_primary" value="true">
                                    
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-check me-2"></i>Complete Registration
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <div class="col-md-4">
                                <!-- Student Info -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user me-2"></i>Student Information
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Name:</strong> {{ student.full_name }}</p>
                                        <p><strong>Student ID:</strong> {{ student.student_id }}</p>
                                        <p><strong>Email:</strong> {{ student.email }}</p>
                                        {% if is_first_enrollment %}
                                            <div class="alert alert-info">
                                                <small>
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    This will be your primary face for attendance.
                                                </small>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <!-- Instructions -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-list-ol me-2"></i>Instructions
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <ol class="small mb-0">
                                            <li class="mb-2">Click "Start Camera" to begin</li>
                                            <li class="mb-2">Position your face in the center</li>
                                            <li class="mb-2">Ensure good lighting</li>
                                            <li class="mb-2">Look directly at the camera</li>
                                            <li class="mb-2">Click "Capture Face"</li>
                                            <li class="mb-0">Complete registration</li>
                                        </ol>
                                    </div>
                                </div>
                                
                                <!-- Tips -->
                                <div class="alert alert-success">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-lightbulb me-2"></i>Tips for Best Results
                                    </h6>
                                    <ul class="small mb-0">
                                        <li>Use good lighting</li>
                                        <li>Remove glasses if possible</li>
                                        <li>Keep a neutral expression</li>
                                        <li>Ensure only your face is visible</li>
                                    </ul>
                                </div>
                                
                                <!-- Skip Option -->
                                {% if not is_first_enrollment %}
                                <div class="text-center mt-4">
                                    <a href="{% url 'student_dashboard' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-skip-forward me-2"></i>Skip for Now
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let video = document.getElementById('video');
        let canvas = document.getElementById('canvas');
        let context = canvas.getContext('2d');
        let capturedImageData = null;
        let stream = null;

        // Start camera
        document.getElementById('startCamera').addEventListener('click', async function() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { width: 640, height: 480 } 
                });
                video.srcObject = stream;
                
                // Show video, hide placeholder
                document.getElementById('cameraPlaceholder').style.display = 'none';
                video.classList.remove('d-none');
                
                // Update button states
                this.disabled = true;
                document.getElementById('captureImage').disabled = false;
                
                showAlert('Camera started successfully! Position your face and click "Capture Face".', 'success');
            } catch (error) {
                console.error('Error accessing camera:', error);
                showAlert('Error accessing camera. Please check permissions and try again.', 'danger');
            }
        });

        // Capture image
        document.getElementById('captureImage').addEventListener('click', function() {
            // Set canvas size to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            // Draw video frame to canvas
            context.drawImage(video, 0, 0, canvas.width, canvas.height);
            
            // Get image data
            capturedImageData = canvas.toDataURL('image/jpeg', 0.8);
            
            // Show preview
            document.getElementById('capturedImage').src = capturedImageData;
            document.getElementById('imagePreview').style.display = 'block';
            
            // Show retake button
            document.getElementById('retakeImage').style.display = 'inline-block';
            this.disabled = true;
            
            // Validate image
            validateImage(capturedImageData);
        });

        // Retake image
        document.getElementById('retakeImage').addEventListener('click', function() {
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('enrollmentForm').style.display = 'none';
            document.getElementById('validationStatus').style.display = 'none';
            document.getElementById('captureImage').disabled = false;
            this.style.display = 'none';
            capturedImageData = null;
        });

        // Validate image (client-side basic validation)
        function validateImage(imageData) {
            try {
                // Basic client-side validation
                if (!imageData || imageData.length < 1000) {
                    showValidationStatus('Image data is too small. Please try again.', 'danger');
                    return;
                }

                // Check if it's a valid base64 image
                if (!imageData.startsWith('data:image/')) {
                    showValidationStatus('Invalid image format. Please try again.', 'danger');
                    return;
                }

                // If basic validation passes, show success and enable form
                showValidationStatus('Image captured successfully! You can now complete registration.', 'success');
                document.getElementById('imageData').value = capturedImageData;
                document.getElementById('enrollmentForm').style.display = 'block';

            } catch (error) {
                console.error('Error validating image:', error);
                showValidationStatus('Error validating image. Please try again.', 'danger');
            }
        }

        // Show validation status
        function showValidationStatus(message, type) {
            const statusDiv = document.getElementById('validationStatus');
            statusDiv.className = `alert alert-${type}`;
            statusDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'} me-2"></i>${message}`;
            statusDiv.style.display = 'block';
        }

        // Show alert
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.enrollment-body');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Form submission
        document.getElementById('enrollmentForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Registering Face...';
            
            // Stop camera
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
        });
    </script>
</body>
</html>
